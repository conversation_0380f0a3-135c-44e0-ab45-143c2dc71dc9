# ABOUTME: Tests for subscription status API endpoint including work_planning feature flag
# ABOUTME: Tests plan-based feature access and authorization logic for Plus/Premium plans

require 'rails_helper'

RSpec.describe Api::V1::SubscriptionController, type: :request do
  describe 'GET /api/v1/subscription_status' do
    let(:user) { create(:user) }
    let(:company) { create(:company) }
    let(:owner_role) { create(:role, name: 'owner') }
    let(:jwt_token) { JwtService.encode_access_token(user.jwt_payload) }
    let(:auth_headers) { { 'Authorization' => "Bearer #{jwt_token}" } }
    
    # Create company user role for testing
    let!(:user_company_role) do
      create(:company_user_role, user: user, company: company, role: owner_role, is_primary: true, active: true)
    end
    
    before do
      ActsAsTenant.current_tenant = company
      # Ensure Redis is available for JWT revocation tests
      Redis.current.with(&:flushdb)
    end

    context 'when company has Free plan' do
      before do
        # Ensure company has no subscription (Free plan)
        company.subscriptions.destroy_all
      end

      it 'returns Free plan with no advanced features' do
        get '/api/v1/subscription_status', headers: auth_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['current_plan']).to eq('Free')
        expect(json_response['available_features']).to eq([])
        expect(json_response['available_features']).not_to include('work_planning')
      end
    end

    context 'when company has Plus plan' do
      before do
        # Create Plus subscription for company
        plan = create(:plan, :plus)
        create(:subscription, company: company, plan: plan, status: 'active')
      end

      it 'returns Plus plan with work_planning feature included' do
        get '/api/v1/subscription_status', headers: auth_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['current_plan']).to eq('plus')
        expect(json_response['available_features']).to include('work_planning')
        expect(json_response['available_features']).to include('booking')
        expect(json_response['available_features']).to include('reservation')
        expect(json_response['available_features']).to include('meeting')
      end
    end

    context 'when company has Premium plan' do
      before do
        # Create Premium subscription for company
        plan = create(:plan, :premium)
        create(:subscription, company: company, plan: plan, status: 'active')
      end

      it 'returns Premium plan with all features including work_planning' do
        get '/api/v1/subscription_status', headers: auth_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['current_plan']).to eq('premium')
        expect(json_response['available_features']).to include('work_planning')
        expect(json_response['available_features']).to include('booking')
        expect(json_response['available_features']).to include('advanced_reporting')
      end
    end

    context 'when unauthenticated' do
      it 'returns unauthorized' do
        get '/api/v1/subscription_status'
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when different tenant company' do
      let(:other_company) { create(:company) }
      let(:other_role) { create(:role, name: 'owner') }
      let(:other_company_jwt_token) do
        # Create JWT with company_id pointing to other_company
        payload = {
          user_id: user.id,
          email: user.email,
          company_id: other_company.id
        }
        JwtService.encode(payload)
      end
      let(:other_company_auth_headers) { { 'Authorization' => "Bearer #{other_company_jwt_token}" } }
      
      # Create company user role for other company
      let!(:user_other_company_role) do
        create(:company_user_role, user: user, company: other_company, role: other_role, is_primary: false, active: true)
      end
      
      before do
        # Set different tenant company
        ActsAsTenant.current_tenant = other_company
      end

      it 'returns correct tenant company subscription status' do
        # Create Plus subscription for the other company
        plan = create(:plan, :plus)
        create(:subscription, company: other_company, plan: plan, status: 'active')

        get '/api/v1/subscription_status', headers: other_company_auth_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['current_plan']).to eq('plus')
        expect(json_response['available_features']).to include('work_planning')
      end
    end
  end
end