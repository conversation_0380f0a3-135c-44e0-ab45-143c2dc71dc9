<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {{ $t('confirm_email', 'Confirm your email') }}
        </h2>
        <p v-if="userEmail" class="mt-2 text-center text-sm text-gray-600">
          {{ $t('confirming_email_for', 'Confirming email for:') }} {{ userEmail }}
        </p>
      </div>
      
      <!-- Loading State -->
      <div v-if="isLoading" class="rounded-md bg-blue-50 p-4">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
          <div>
            <p class="text-sm font-medium text-blue-800">
              {{ $t('confirming_email', 'Confirming your email...') }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- Success Message -->
      <div v-if="successMessage" class="rounded-md bg-green-50 p-4">
        <div class="flex">
          <div class="ml-3">
            <p class="text-sm font-medium text-green-800">
              {{ successMessage }}
            </p>
            <p class="mt-2 text-sm text-green-700">
              {{ $t('redirecting_dashboard', 'Redirecting to dashboard...') }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- Error Messages -->
      <div v-if="errorMessage" class="rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              {{ $t('email_confirmation_failed', 'Email confirmation failed') }}
            </h3>
            <p class="mt-2 text-sm text-red-700">
              {{ errorMessage }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- Invalid Token Warning -->
      <div v-if="!confirmationToken && !isLoading && !successMessage" class="rounded-md bg-yellow-50 p-4">
        <div class="flex">
          <div class="ml-3">
            <p class="text-sm font-medium text-yellow-800">
              {{ $t('invalid_confirmation_link', 'Invalid email confirmation link. Please request a new one.') }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- Resend confirmation email form -->
      <div v-if="showResendForm" class="mt-8 space-y-6">
        <div class="rounded-md bg-blue-50 p-4">
          <p class="text-sm text-blue-700">
            {{ $t('need_new_confirmation', 'Need a new confirmation email?') }}
          </p>
        </div>
        
        <form @submit.prevent="handleResendConfirmation">
          <div>
            <label for="email" class="sr-only">{{ $t('front.email', 'Email') }}</label>
            <input
              id="email"
              v-model="resendEmail"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              :placeholder="$t('front.email', 'Email')"
            />
          </div>
          
          <div class="mt-4">
            <button
              type="submit"
              :disabled="isResending"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <span v-if="isResending">{{ $t('sending', 'Sending...') }}</span>
              <span v-else>{{ $t('resend_confirmation', 'Resend confirmation email') }}</span>
            </button>
          </div>
        </form>
        
        <!-- Resend success message -->
        <div v-if="resendMessage" class="rounded-md bg-green-50 p-4 mt-4">
          <p class="text-sm font-medium text-green-800">
            {{ resendMessage }}
          </p>
        </div>
      </div>
      
      <!-- Navigation Links -->
      <div class="text-center mt-6">
        <router-link 
          :to="{ name: 'login', params: { locale: $route.params.locale } }" 
          class="font-medium text-indigo-600 hover:text-indigo-500"
        >
          {{ $t('back_to_login', 'Back to login') }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import AuthService from '@/services/authService'
import { sendFlashMessage } from '@/utils/flashMessage'

export default {
  name: 'EmailConfirmationView',
  data() {
    return {
      confirmationToken: null,
      userEmail: null,
      isLoading: false,
      successMessage: '',
      errorMessage: '',
      showResendForm: false,
      resendEmail: '',
      isResending: false,
      resendMessage: ''
    }
  },
  async created() {
    this.confirmationToken = this.$route.query.token
    
    if (this.confirmationToken) {
      await this.confirmEmail()
    } else {
      this.showResendForm = true
    }
  },
  methods: {
    ...mapActions('user', ['login']),
    
    async confirmEmail() {
      this.isLoading = true
      this.errorMessage = ''
      this.successMessage = ''
      
      try {
        const response = await AuthService.confirmEmail(this.confirmationToken)
        
        if (response.success) {
          this.successMessage = response.message || this.$t('email_confirmed_success', 'Email confirmed successfully! You are now logged in.')
          this.userEmail = response.user?.email
          
          // The user is already logged in by AuthService.confirmEmail.
          // We just need to redirect.
          setTimeout(() => {
            const redirect = this.$route.query.redirect || { 
              name: 'dashboard', 
              params: { locale: this.$route.params.locale } 
            }
            this.$router.push(redirect)
          }, 3000)
        } else {
          // This path may not be reachable if AuthService throws an error, but good for defense.
          this.handleConfirmationError(response.error || 'Email confirmation failed')
        }
      } catch (error) {
        console.error('Email confirmation error:', error)
        this.handleConfirmationError(error.message || this.$t('confirmation_network_error', 'An error occurred. Please try again.'))
      } finally {
        this.isLoading = false
      }
    },
    
    handleConfirmationError(message) {
      this.errorMessage = message
      this.showResendForm = true
      
      // Pre-fill email if we have it from the URL or previous attempt
      if (this.$route.query.email) {
        this.resendEmail = this.$route.query.email
      }
    },
    
    async handleResendConfirmation() {
      if (!this.resendEmail) return
      
      this.isResending = true
      this.resendMessage = ''
      
      try {
        const response = await AuthService.resendEmailConfirmation(this.resendEmail)
        
        if (response.success) {
          this.resendMessage = response.message || this.$t('confirmation_resent', 'Confirmation email sent. Please check your inbox.')
          this.resendEmail = '' // Clear the form
        }
      } catch (error) {
        console.error('Resend confirmation error:', error)
        sendFlashMessage(this.$t('resend_failed', 'Failed to resend confirmation email. Please try again.'), 'error')
      } finally {
        this.isResending = false
      }
    }
  }
}
</script>