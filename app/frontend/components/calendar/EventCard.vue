<!-- ABOUTME: Card component for displaying events in the sidebar for viewing and interaction -->
<!-- ABOUTME: Provides event information with nice boxes and dates similar to unscheduled works -->

<template>
  <div class="event-card" :class="{ 'event-rejected': event.status === 'rejected' }">
    <div class="card-header">
      <div class="event-title" :class="{ 'rejected-text': event.status === 'rejected' }">
        {{ eventTypeText }}
      </div>
      <div class="event-date">
        {{ formatEventDate() }}
      </div>
     
    </div>
    
    <!-- Event User Owner -->
    <div v-if="eventUser" class="event-user">
      <User :size="12" />
      <span class="user-name">{{ eventUser.first_name }} {{ eventUser.last_name }}</span>
    </div>
    
    <div class="event-details">
      <div v-if="event.event_type" class="event-type" :class="eventTypeClass">
        {{ $t(`event_type.${event.event_type}`, event.event_type) }}
      </div>

       <!-- Rejected Status Badge -->
      <div v-if="event.status === 'rejected'" class="status-rejected">
        {{ $t('rejected', 'Zamítnuto') }}
      </div>

      <!-- Approved Status -->
      <div v-if="event.status === 'approved'" class="status-approved">
        {{ $t('approved', 'Schváleno') }}
      </div>
      
      <div v-if="eventDuration" class="duration">
        <Calendar :size="12" />
        {{ eventDuration }}
      </div>
    </div>
    
    <div v-if="event.place" class="event-place">
      <MapPin :size="12" />
      {{ event.place }}
    </div>
    
    <div v-if="event.description" class="event-description">
      {{ truncateText(event.description, 60) }}
    </div>

    <!-- Event Actions -->
    <div v-if="showActions" class="event-actions">
      <!-- Owner Actions -->
      <template v-if="canEditEvent">
        <button @click="handleDelete" class="text-link-action danger">
          {{ $t('delete', 'Smazat') }}
        </button>
      </template>
      
      <!-- Manager Approval Actions -->
      <template v-if="canApproveEvent && event.status === 'pending'">
        <button @click="handleApprove" class="text-link-action">
          {{ $t('approve', 'Schválit') }}
        </button>
        <button @click="handleReject" class="text-link-action">
          {{ $t('reject', 'Zamítnout') }}
        </button>
      </template>
      
    </div>
  </div>
</template>

<script>
import { Calendar, MapPin, User, CheckCheck, X } from 'lucide-vue-next';
import { mapGetters } from 'vuex';

export default {
  name: 'EventCard',
  components: {
    Calendar,
    MapPin,
    User,
    CheckCheck,
    X
  },
  props: {
    event: {
      type: Object,
      required: true
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapGetters('userStore', ['currentUser', 'isManager', 'isOwner']),
    ...mapGetters('calendarStore', ['currentUserContractId']),
    
    eventTypeClass() {
      return `event-type-${this.event.event_type}`;
    },
    
    eventTypeText() {
      return this.$t(`event_type.${this.event.event_type}`, this.event.event_type);
    },
    
    eventDuration() {
      if (!this.event.start_time || !this.event.end_time) return '';
      
      const start = new Date(this.event.start_time);
      const end = new Date(this.event.end_time);
      const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        return '1 den';
      } else if (diffDays > 1) {
        return `${diffDays} dní`;
      }
      
      return '1 den';
    },

    eventUser() {
      // Event user should be available in the event object
      return this.event.user || this.event.contract || null;
    },

    canEditEvent() {
      // All users (including owners) can only edit their own events
      if (this.currentUserContractId && this.event.contract) {
        return this.currentUserContractId === this.event.contract.id;
      }
      return false;
    },

    canApproveEvent() {
      // Owners can approve any event
      if (this.isOwner) {
        return true;
      }
      // Managers can approve vacation events
      return this.isManager && this.event.event_type === 'vacation';
    }
  },
  methods: {
    formatEventDate() {
      if (!this.event.start_time) return '';
      
      const start = new Date(this.event.start_time);
      const end = new Date(this.event.end_time);
      
      const startFormatted = start.toLocaleDateString(this.$i18n.locale, { 
        month: 'short', 
        day: 'numeric' 
      });
      
      // If multi-day event, show range
      if (end && start.toDateString() !== end.toDateString()) {
        const endFormatted = end.toLocaleDateString(this.$i18n.locale, { 
          month: 'short', 
          day: 'numeric' 
        });
        return `${startFormatted} - ${endFormatted}`;
      }
      
      return startFormatted;
    },
    
    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },

    async handleDelete() {
      if (confirm(this.$t('confirm_delete_event', 'Opravdu chcete smazat tuto událost?'))) {
        try {
          await this.$store.dispatch('calendarStore/deleteEvent', this.event.id);
          this.$emit('event-updated');
        } catch (error) {
          // Error message will be shown by the action
        }
      }
    },

    async handleApprove() {
      try {
        await this.$store.dispatch('calendarStore/approveEvent', this.event.id);
        this.$emit('event-updated');
      } catch (error) {
        // Error message will be shown by the action
      }
    },

    async handleReject() {
      if (confirm(this.$t('confirm_reject_event', 'Opravdu chcete zamítnout tuto událost?'))) {
        try {
          await this.$store.dispatch('calendarStore/rejectEvent', this.event.id);
          this.$emit('event-updated');
        } catch (error) {
          // Error message will be shown by the action
        }
      }
    }
  }
};
</script>

<style scoped>
.event-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.event-card:hover {
  border-color: #22C55E;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.event-title {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
}

.event-date {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
}

.event-details {
  display: flex;
  gap: 6px;
  margin-bottom: 4px;
  align-items: center;
  flex-wrap: wrap;
}

.duration {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.event-type {
  font-size: 10px;
  padding: 2px 10px;
  border-radius: 12px;
  text-transform: uppercase;
  font-weight: 500;
}

.event-type-vacation {
  background-color: #e0f2fe;
  color: #0277bd;
}

.event-type-travel {
  background-color: #fff3e0;
  color: #f57c00;
}

.event-type-illness {
  background-color: #fce4ec;
  color: #c2185b;
}

.event-type-family_sick {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.event-type-day_care {
  background-color: #e8f5e8;
  color: #2d7d32;
}

.event-type-other {
  background-color: #f3f4f6;
  color: #6b7280;
}

.event-place,
.event-description {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #6b7280;
  margin-bottom: 3px;
}

.event-description {
  line-height: 1.4;
  margin-top: 2px;
}

/* Event User */
.event-user {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #374151;
  margin-bottom: 6px;
  font-weight: 500;
}

.user-name {
  color: #1f2937;
}

/* Event Actions */
.event-actions {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  align-items: center;
}

.status-approved {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;

  background-color: #e8f5e8;
  color: #2d7d32;
  text-transform: uppercase;
}

.status-rejected {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 10px;
  border-radius: 12px;
  font-weight: 500;
  background-color: #ffebee;
  color: #c62828;
  font-size: 10px;
  text-transform: uppercase;
}

/* Rejected event styling */
.event-rejected {
  opacity: 0.7;
  border-color: #ffcdd2;
  background-color: #fafafa;
}

.event-rejected:hover {
  border-color: #ef5350;
  opacity: 0.9;
}

.rejected-text {
  text-decoration: line-through;
  color: #757575;
}
</style>