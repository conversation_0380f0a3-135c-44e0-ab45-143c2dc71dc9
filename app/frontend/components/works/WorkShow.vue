<template>
  <div class="work-show-card">
    <div class="work-header">
      <div class="work-title-section">
        <LandPlot :size="14" class="text-gray-500 flex-shrink-0" />
        <h3 class="work-title">{{ work.title }}</h3>
      </div>
      <div class="relative" ref="dropdown">
        <button class="action-button" @click="toggleDropdown">
          <MoreVertical :size="16" />
        </button>
        <div
          v-if="showDropdown"
          class="dropdown-menu"
        >
          <a href="#"
             class="dropdown-item"
             @click.prevent="editWork">
            <Clipboard :size="12" class="inline mr-2" />
            {{ $t('edit', 'Upravit') }}
          </a>
          <a href="#"
             v-if="isManager"
             class="dropdown-item text-orange-600 hover:bg-orange-50"
             @click.prevent="confirmForceClose">
            <AlertTriangle :size="12" class="inline mr-2" />
            {{ $t('works.force_close_activities', 'Force Close Activities') }}
          </a>
          <a href="#"
             class="dropdown-item text-red-600 hover:bg-red-50"
             @click.prevent="confirmDelete">
            <FolderOpen :size="12" class="inline mr-2" />
            {{ $t('delete', 'Smazat') }}
          </a>
          <a href="#"
            v-if="!isAssigned"
            @click="takeAssignment"
            class="dropdown-item"
            :disabled="takingAssignment"
          >
            <UserPlus :size="12" class="inline mr-2" />
            {{ $t('works.assignments.take_assignment', 'Převzít zakázku') }}
          </a>
          <a href="#"
            v-else-if="canLeaveAssignment"
            @click="leaveAssignment"
            class="dropdown-item"
            :disabled="leavingAssignment"
          >
            <UserMinus :size="12" class="inline mr-2" />
            {{ $t('works.assignments.leave_assignment', 'Opustit zakázku') }}
          </a>
        </div>
      </div>
    </div>

    <div class="work-details">
      <template v-if="work.confirmed_time">
        <div class="detail-item">
          <Clock :size="12" class="detail-icon" />
          <span class="detail-text">{{ formatTime(work.confirmed_time) }}</span>
        </div>
      </template>

      <template v-else-if="work.specific_time || translatedTime">
        <div class="detail-item">
          <Clock :size="12" class="detail-icon" />
          <span class="detail-text">{{ translatedTime }}</span>
          <span v-if="work.specific_time" class="detail-subtext">
            {{ $t('preferred_time', 'preferovaný čas') }}: {{ formatTime(work.specific_time) }}
          </span>
        </div>
      </template>

      <template v-if="work.location">
        <div class="detail-item">
          <MapPin :size="12" class="detail-icon" />
          <span class="detail-text">{{ work.location }}</span>
        </div>
      </template>
    </div>

    <p v-if="work.description" class="work-description">
      {{ work.description }}
    </p>

    <div class="work-additional-info">
      <div v-if="work.scheduled_start_date || work.scheduled_end_date" class="detail-item">
        <Calendar :size="12" class="detail-icon" />
        <span class="detail-text">
          {{ work.scheduled_start_date ? formatDate(work.scheduled_start_date) : '' }}
          {{ work.scheduled_end_date ? '- ' + formatDate(work.scheduled_end_date) : '' }}
        </span>
      </div>

      <div class="assignments-section">
        <div v-if="work.work_assignments && work.work_assignments.length > 0" class="detail-item">
          <CircleUserRound :size="12" class="detail-icon" />
          <div class="assignments-list">
            <span v-for="(assignment, index) in work.work_assignments" :key="assignment.id" class="assignment-name">
              {{ assignment.contract.first_name }} {{ assignment.contract.last_name }}{{ index < work.work_assignments.length - 1 ? ', ' : '' }}
            </span>
          </div>
        </div>
        <div v-else class="detail-item">
          <Users :size="12" class="detail-icon" />
          <span class="detail-text text-gray-500">{{ $t('works.assignments.no_assignments', 'Žádná přiřazení') }}</span>
        </div>
      </div>

      <div class="work-badges">
        <span v-if="work.status" class="status-badge" :class="getStatusClass(work.status)">
          {{ getStatusText(work.status) }}
        </span>
        <span v-if="work.booking" class="status-badge" :class="getBookingStatusClass(work.booking.status)">
          {{ getBookingStatusText(work.booking.status) }}
        </span>
        <span v-if="work.booking && work.booking.preferred_date && work.booking.preferred_date !== work.scheduled_start_date" class="status-badge badge-warning">
          {{ $t('works.change_to', 'Změna na') }}: {{ formatDate(work.booking.preferred_date) }}
        </span>
        <span v-if="work.booking && work.booking.preferred_period && work.booking.preferred_period !== work.preferred_period" class="status-badge badge-warning">
          {{ $t('works.change_to', 'Změna na') }}: {{ getTranslatedBookingPeriod(work.booking.preferred_period) }}
        </span>
        <span v-if="work.booking && work.booking.specific_time && work.booking.specific_time !== work.specific_time" class="status-badge badge-warning">
          {{ $t('works.change_to', 'Změna na') }}: {{ formatTime(work.booking.specific_time) }}
        </span>
      </div>
    </div>

  </div>
</template>

<script>
import axios from 'axios';
import { Calendar, Clipboard, MapPin, MoreVertical, FolderOpen, CircleUserRound, Clock, LandPlot, Users, UserPlus, UserMinus, AlertTriangle } from 'lucide-vue-next';
import { sendFlashMessage } from '/utils/flashMessage';
import authorizationMixin from '/mixins/authorizationMixin';

export default {
  mixins: [authorizationMixin],
  components: {
    MapPin, Clipboard, MoreVertical, Calendar, FolderOpen, CircleUserRound, Clock, LandPlot, Users, UserPlus, UserMinus, AlertTriangle
  },
  props: {
    work: {
      type: Object,
      required: true
    },
    inline: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    translatedTime() {
      const timeTranslations = {
        morning: this.$t('morning', 'Dopoledne'),
        afternoon: this.$t('afternoon', 'Odpoledne'),
        allday: this.$t('all_day', 'Celý den')
      };
      return timeTranslations[this.work.preferred_period] || this.work.preferred_period;
    },
    workStatusClass() {
      return this.getStatusClass(this.work.status);
    },
    isAssigned() {
      // Check if current user is assigned to this work
      if (!this.currentUserContractId) return false;
      return this.work.work_assignments?.some(a => a.contract_id === this.currentUserContractId) || false;
    },
    canLeaveAssignment() {
      // Can leave if assigned and there's more than one assignee
      return this.isAssigned && this.work.work_assignments?.length > 1;
    }
  },
  data() {
    return {
      showDropdown: false,
      takingAssignment: false,
      leavingAssignment: false,
      currentUserContractId: null
    };
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
    document.addEventListener('work-updated', this.handleWorkUpdated);
    this.fetchCurrentUserContract();
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
    document.removeEventListener('work-updated', this.handleWorkUpdated);
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (this.showDropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    handleWorkUpdated(event) {
      // Update this work if it's the one that was updated
      if (event.detail?.work?.id === this.work.id) {
        Object.assign(this.work, event.detail.work);
        this.$emit('updated', this.work);
      }
    },
    getStatusClass(status) {
      switch(status) {
        case 'scheduled': return 'scheduled';
        case 'in_progress': return 'in_progress';
        case 'completed': return 'completed';
        case 'cancelled': return 'cancelled';
        case 'rescheduled': return 'rescheduled';
        case 'unprocessed': return 'unprocessed';
        default: return 'inactive';
      }
    },
    getStatusText(status) {
      switch(status) {
        case 'scheduled': return this.$t('works.scheduled_work', 'Naplánovaná zakázka');
        case 'in_progress': return this.$t('works.in_progress_work', 'Zakázka probíhá');
        case 'completed': return this.$t('works.completed_work', 'Dokončená zakázka');
        case 'cancelled': return this.$t('works.cancelled_work', 'Zrušená zakázka');
        case 'rescheduled': return this.$t('works.booking_change_notice', 'Pozor, změna rezervace');
        case 'unprocessed': return this.$t('works.unprocessed_work', 'Nová zakázka');
        default: return status;
      }
    },
    getBookingStatusClass(status) {
      switch(status) {
        case 'pending': return 'pending';
        case 'confirmed': return 'confirmed';
        case 'completed': return 'completed';
        case 'cancelled': return 'cancelled';
        case 'rescheduled': return 'rescheduled';
        default: return 'inactive';
      }
    },
    getBookingStatusText(status) {
      switch(status) {
        case 'pending': return this.$t('booking.new', 'Nová rezervace');
        case 'confirmed': return this.$t('booking.confirmed', 'Potvrzená rezervace');
        case 'completed': return this.$t('booking.completed', 'Dokončená rezervace');
        case 'cancelled': return this.$t('booking.cancelled', 'Zrušená rezervace');
        case 'rescheduled': return this.$t('booking.rescheduled', 'Přeplánovaná rezervace');
        default: return status;
      }
    },
    getTranslatedBookingPeriod(period) {
      const periodTranslations = {
        morning: this.$t('morning', 'Dopoledne'),
        afternoon: this.$t('afternoon', 'Odpoledne'),
      };
      return periodTranslations[period] || period;
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString('cs-CZ', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
      });
    },
    formatTime(dateString) {
      const date = new Date(dateString);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    editWork() {
      this.showDropdown = false;
      const event = new CustomEvent('open-central-modal', {
        detail: {
          componentName: 'WorkForm', 
          title: `${this.$t('works.edit_work', 'Upravit zakázku')}: ${this.work.title}`,
          props: { work: this.work }
        }
      });
      document.dispatchEvent(event);
    },
    confirmForceClose() {
      if (confirm(this.$t('works.confirm_force_close', 'Force close all active activities on this work?'))) {
        this.forceCloseActivities();
      }
      this.showDropdown = false;
    },
    async forceCloseActivities() {
      try {
        const response = await axios.post(`/api/v1/works/${this.work.id}/force_close_activities`);
        sendFlashMessage(response.data.message || this.$t('works.activities_force_closed', 'Activities force closed'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.error || this.$t('works.force_close_error', 'Error closing activities');
        sendFlashMessage(errorMsg, 'error');
      }
    },
    confirmDelete() {
      if (confirm(this.$t('works.confirm_delete', 'Opravdu chcete smazat tuto zakázku?'))) {
        this.deleteWork();
      }
      this.showDropdown = false;
    },
    async deleteWork() {
      try {
        await axios.delete(`/api/v1/works/${this.work.id}`, { 
          headers: { 'Accept': 'application/json' } 
        });
        this.$emit('deleted', this.work.id);
        sendFlashMessage(this.$t('works.deleted_success', 'Práce byla úspěšně smazána'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.errors?.join(', ') || 
                        error.response?.data?.error || 
                        this.$t('works.delete_error', 'Chyba při mazání práce');
        sendFlashMessage(errorMsg, 'error');
      }
    },
    async takeAssignment() {
      this.takingAssignment = true;
      try {
        const response = await axios.post(`/works/${this.work.id}/take_assignment.json`);
        // Update the work with new assignment data
        if (response.data.work) {
          Object.assign(this.work, response.data.work);
          this.$emit('updated', this.work);
        }
        sendFlashMessage(response.data.message || this.$t('works.assignments.assignment_taken', 'Přiřazení bylo úspěšné'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.error || this.$t('works.assignments.take_error', 'Chyba při přiřazení');
        sendFlashMessage(errorMsg, 'error');
      } finally {
        this.takingAssignment = false;
      }
    },
    async fetchCurrentUserContract() {
      try {
        const response = await this.$store.dispatch('contractsStore/fetchColleagues', { includeSelf: true });
        this.currentUserContractId = response.current_user_contract_id;
      } catch (error) {
        console.error('Error fetching current user contract:', error);
      }
    },
    async leaveAssignment() {
      if (!confirm(this.$t('works.assignments.confirm_leave', 'Opravdu chcete opustit tuto práci?'))) {
        return;
      }
      
      this.leavingAssignment = true;
      try {
        const response = await axios.delete(`/works/${this.work.id}/leave_assignment.json`);
        // Update the work with new assignment data
        if (response.data.work) {
          Object.assign(this.work, response.data.work);
          this.$emit('updated', this.work);
        }
        sendFlashMessage(response.data.message || this.$t('works.assignments.assignment_left', 'Přiřazení bylo odebráno'), 'success');
      } catch (error) {
        const errorMsg = error.response?.data?.error || this.$t('works.assignments.leave_error', 'Chyba při opuštění práce');
        sendFlashMessage(errorMsg, 'error');
      } finally {
        this.leavingAssignment = false;
      }
    }
  }
};
</script>

<style scoped>
.work-show-card {
  background-color: #e0f2fe;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  transition: transform 0.2s ease;
}

.work-show-card:hover {
  transform: translateY(-1px);
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.work-title-section {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.work-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
}

.action-button {
  padding: 0.25rem;
  color: #6b7280;
  background: none;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.action-button:hover {
  color: #374151;
  background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  padding: 0.25rem;
  width: 12rem;
  background: white;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  z-index: 10;
}

.dropdown-item {
  display: block;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  color: #374151;
  text-decoration: none;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
}

.work-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-icon {
  color: #6b7280;
  flex-shrink: 0;
}

.detail-text {
  font-size: 0.75rem;
  color: #374151;
  font-weight: 500;
}

.detail-subtext {
  font-size: 0.6875rem;
  color: #6b7280;
  margin-left: 0.5rem;
}

.work-description {
  font-size: 0.6875rem;
  color: #6b7280;
  margin: 0.5rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.work-additional-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.assignments-section {
  margin-top: 0.25rem;
}

.assignments-list {
  display: flex;
  flex-wrap: wrap;
}

.assignment-name {
  font-size: 0.75rem;
  color: #374151;
}

.work-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  justify-content: flex-end;
}

.status-badge {
  font-size: 0.6875rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Status badge colors - keeping existing logic */
.scheduled {
  background-color: #e0f2fe;
  color: #0277bd;
}

.in_progress {
  background-color: #fff3e0;
  color: #f57c00;
}

.completed {
  background-color: #e8f5e8;
  color: #2d7d32;
}

.cancelled {
  background-color: #fce4ec;
  color: #c2185b;
}

.rescheduled {
  background-color: #fff3e0;
  color: #f57c00;
}

.unprocessed {
  background-color: #f3f4f6;
  color: #374151;
}

.inactive {
  background-color: #f5f5f5;
  color: #757575;
}

.pending {
  background-color: #fefce8;
  color: #854d0e;
}

.confirmed {
  background-color: #e8f5e8;
  color: #2d7d32;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .work-show-card {
    padding: 0.375rem;
  }

  .work-title {
    font-size: 0.8125rem;
  }

  .detail-text {
    font-size: 0.6875rem;
  }

  .work-description {
    font-size: 0.625rem;
  }

  .status-badge {
    font-size: 0.625rem;
    padding: 0.1rem 0.3rem;
  }
}
</style>
