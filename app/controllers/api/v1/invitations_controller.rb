module Api
  module V1
    class InvitationsController < ApiController
      include JwtLoginHelpers
      
      # Skip authentication for public invitation endpoints
      skip_before_action :authenticate_user!, only: [:show, :accept]
      skip_before_action :set_current_tenant, only: [:show, :accept]

      # POST /api/v1/invitations
      def create
        start_time = Time.current
        
        # Validate required parameters
        email = params[:email]&.strip&.downcase
        first_name = params[:first_name]&.strip
        last_name = params[:last_name]&.strip
        
        unless email.present? && first_name.present? && last_name.present?
          render json: { 
            error: I18n.t('controllers.invitations.errors.email_first_last_required'),
            details: {
              email: email.blank? ? I18n.t('invitations.details.email_required') : nil,
              first_name: first_name.blank? ? I18n.t('invitations.details.first_name_required') : nil,
              last_name: last_name.blank? ? I18n.t('invitations.details.last_name_required') : nil
            }.compact
          }, status: :unprocessable_entity
          return
        end
        
        # Validate email format
        unless email.match?(URI::MailTo::EMAIL_REGEXP)
          render json: { 
            error: I18n.t('controllers.invitations.errors.invalid_email_format'),
            details: { email: I18n.t('invitations.details.email_invalid_format') }
          }, status: :unprocessable_entity
          return
        end
        
        # Send invitation
        result = InvitationService.send_invitation(
          email: email,
          first_name: first_name,
          last_name: last_name,
          sender: current_user,
          company: ActsAsTenant.current_tenant
        )
        
        if result[:success]
          render json: {
            success: true,
            message: result[:message],
            type: result[:type],
            processing_time: (Time.current - start_time).round(3)
          }, status: :created
        else
          render json: {
            success: false,
            error: result[:error],
            details: result[:details]
          }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error "[ERROR] Invitation creation failed: #{e.message}"
        render json: { 
          error: I18n.t('controllers.invitations.errors.failed_to_process'),
          details: { invitation: I18n.t('invitations.details.could_not_be_processed') }
        }, status: :internal_server_error
      end

      # GET /api/v1/invitations/:token
      def show
        start_time = Time.current
        
        # Get and validate token
        invitation_token = params[:token]&.strip
        
        unless invitation_token.present?
          render json: { 
            error: I18n.t('controllers.invitations.errors.token_required'),
            details: { token: I18n.t('invitations.details.token_required') }
          }, status: :unprocessable_entity
          return
        end
        
        # Validate and find invitation data
        invitation_data = InvitationService.validate_token(invitation_token)
        
        unless invitation_data
          render json: { 
            valid: false,
            error: I18n.t('controllers.invitations.errors.invalid_or_expired_token')
          }, status: :unprocessable_entity
          return
        end
        
        # Extract invitation details
        email = invitation_data['email']
        company_id = invitation_data['company_id']
        first_name = invitation_data['first_name']
        last_name = invitation_data['last_name']
        sender_id = invitation_data['sender_id']
        
        # Get company and sender information
        company = Company.find_by(id: company_id)
        sender = User.find_by(id: sender_id)
        
        unless company && sender
          render json: { 
            valid: false,
            error: I18n.t('controllers.invitations.errors.invalid_invitation_data')
          }, status: :unprocessable_entity
          return
        end
        
        # Check if user already exists
        existing_user = User.find_by(email: email)
        invitation_type = existing_user ? 'existing_user' : 'new_user'
        
        # Check if existing user is already connected to company
        already_connected = false
        if existing_user
          existing_role = existing_user.company_user_roles.find_by(company: company)
          already_connected = existing_role.present?
        end
        
        render json: {
          valid: true,
          invitation: {
            email: email,
            first_name: first_name,
            last_name: last_name,
            company: {
              id: company.id,
              name: company.name
            },
            sender: {
              id: sender.id,
              first_name: sender.user_profile&.first_name || I18n.t('common.unknown'),
              last_name: sender.user_profile&.last_name || I18n.t('common.user'),
              email: sender.email
            },
            type: invitation_type,
            already_connected: already_connected,
            created_at: invitation_data['created_at']
          },
          processing_time: (Time.current - start_time).round(3)
        }, status: :ok
      rescue => e
        Rails.logger.error "[ERROR] Invitation details lookup failed: #{e.message}"
        render json: { 
          valid: false,
          error: I18n.t('controllers.invitations.errors.failed_to_retrieve_details')
        }, status: :internal_server_error
      end

      # POST /api/v1/invitations/:token/accept
      def accept
        start_time = Time.current
        
        invitation_token = params[:token]&.strip
        
        unless invitation_token.present?
          render json: { 
            error: I18n.t('controllers.invitations.errors.token_required'),
            details: { token: I18n.t('invitations.details.token_required') }
          }, status: :unprocessable_entity
          return
        end
        
        # Validate invitation token
        invitation_data = InvitationService.validate_token(invitation_token)
        
        unless invitation_data
          render json: { 
            success: false,
            error: I18n.t('controllers.invitations.errors.invalid_or_expired_token')
          }, status: :unprocessable_entity
          return
        end
        
        email = invitation_data['email']
        company_id = invitation_data['company_id']
        first_name = invitation_data['first_name']
        last_name = invitation_data['last_name']
        sender_id = invitation_data['sender_id']
        
        # Check if user exists
        user = User.find_by(email: email)
        
        if user
          # Existing user - accept company connection
          result = InvitationService.accept_invitation(token: invitation_token, user: user)
          
          if result[:success]
            render json: {
              success: true,
              message: result[:message],
              user_type: 'existing',
              processing_time: (Time.current - start_time).round(3)
            }, status: :ok
          else
            render json: {
              success: false,
              error: result[:error],
              details: result[:details]
            }, status: :unprocessable_entity
          end
        else
          # New user - complete registration
          password = params[:password]
          password_confirmation = params[:password_confirmation]
          
          result = InvitationService.new.complete_user_registration(
            email: email,
            company_id: company_id,
            sender_id: sender_id,
            invitation_token: invitation_token,
            password: password,
            password_confirmation: password_confirmation
          )
          
          if result[:success]
            # CRITICAL FIX: Automatically log in new user with JWT tokens
            # This ensures user is logged in after invitation acceptance
            user = result[:user]
            handle_successful_jwt_login(user, 
              event_name: 'invitation_acceptance', 
              start_time: start_time,
              message: I18n.t('invitations.messages.registration_completed_and_logged_in'),
              status: :created
            )
          else
            render json: {
              success: false,
              error: result[:error],
              details: result[:details]
            }, status: :unprocessable_entity
          end
        end
      rescue => e
        Rails.logger.error "[ERROR] Invitation acceptance failed: #{e.message}"
        render json: { 
          success: false,
          error: I18n.t('controllers.invitations.errors.failed_to_accept'),
          details: { invitation: I18n.t('invitations.details.could_not_be_processed') }
        }, status: :internal_server_error
      end
    end
  end
end